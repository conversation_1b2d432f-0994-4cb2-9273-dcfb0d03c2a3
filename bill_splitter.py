#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯云账单拆分工具
用于分析和拆分腾讯云账单CSV文件
"""

import pandas as pd
import numpy as np
from datetime import datetime
import os
import sys

class BillSplitter:
    def __init__(self, csv_file_path):
        """
        初始化账单拆分器
        
        Args:
            csv_file_path (str): CSV文件路径
        """
        self.csv_file_path = csv_file_path
        self.df = None
        self.load_data()
    
    def load_data(self):
        """加载CSV数据"""
        try:
            print(f"正在加载文件: {self.csv_file_path}")
            self.df = pd.read_csv(self.csv_file_path)
            print(f"成功加载 {len(self.df)} 条记录")
            
            # 转换数值列
            numeric_columns = [
                'Total Cost', 'Total Amount After Discount (Excluding Tax)',
                'Amount Before Tax', 'TaxAmount', 'Total Cost (Including Tax)'
            ]
            
            for col in numeric_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
            
            print("数据加载完成")
            
        except Exception as e:
            print(f"加载数据时出错: {e}")
            sys.exit(1)
    
    def get_summary_stats(self):
        """获取总体统计信息"""
        total_cost = self.df['Total Cost (Including Tax)'].sum()
        total_before_tax = self.df['Amount Before Tax'].sum()
        total_tax = self.df['TaxAmount'].sum()
        
        print("\n=== 账单总体统计 ===")
        print(f"总记录数: {len(self.df):,}")
        print(f"含税总金额: ${total_cost:,.2f}")
        print(f"税前总金额: ${total_before_tax:,.2f}")
        print(f"税费总额: ${total_tax:,.2f}")
        print(f"账单月份: {self.df['Consumption Month'].iloc[0]}")
        
        return {
            'total_records': len(self.df),
            'total_cost_with_tax': total_cost,
            'total_before_tax': total_before_tax,
            'total_tax': total_tax
        }
    
    def split_by_account(self):
        """按账户拆分"""
        print("\n=== 按账户拆分 ===")
        
        account_summary = self.df.groupby('Owner Account ID').agg({
            'Total Cost (Including Tax)': 'sum',
            'Amount Before Tax': 'sum',
            'TaxAmount': 'sum',
            'InstanceID': 'count'
        }).round(2)
        
        account_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
        account_summary = account_summary.sort_values('含税总金额', ascending=False)
        
        print(account_summary)
        
        # 保存到CSV
        output_file = 'account_split.csv'
        account_summary.to_csv(output_file, encoding='utf-8-sig')
        print(f"\n账户拆分结果已保存到: {output_file}")
        
        return account_summary
    
    def split_by_project(self):
        """按项目拆分"""
        print("\n=== 按项目拆分 ===")
        
        project_summary = self.df.groupby('ProjectName').agg({
            'Total Cost (Including Tax)': 'sum',
            'Amount Before Tax': 'sum',
            'TaxAmount': 'sum',
            'InstanceID': 'count'
        }).round(2)
        
        project_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
        project_summary = project_summary.sort_values('含税总金额', ascending=False)
        
        print(project_summary.head(20))  # 显示前20个项目
        
        # 保存到CSV
        output_file = 'project_split.csv'
        project_summary.to_csv(output_file, encoding='utf-8-sig')
        print(f"\n项目拆分结果已保存到: {output_file}")
        
        return project_summary
    
    def split_by_product(self):
        """按产品类型拆分"""
        print("\n=== 按产品类型拆分 ===")
        
        product_summary = self.df.groupby('ProductName').agg({
            'Total Cost (Including Tax)': 'sum',
            'Amount Before Tax': 'sum',
            'TaxAmount': 'sum',
            'InstanceID': 'count'
        }).round(2)
        
        product_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
        product_summary = product_summary.sort_values('含税总金额', ascending=False)
        
        print(product_summary)
        
        # 保存到CSV
        output_file = 'product_split.csv'
        product_summary.to_csv(output_file, encoding='utf-8-sig')
        print(f"\n产品拆分结果已保存到: {output_file}")
        
        return product_summary
    
    def split_by_account_and_project(self):
        """按账户和项目双维度拆分"""
        print("\n=== 按账户和项目双维度拆分 ===")
        
        account_project_summary = self.df.groupby(['Owner Account ID', 'ProjectName']).agg({
            'Total Cost (Including Tax)': 'sum',
            'Amount Before Tax': 'sum',
            'TaxAmount': 'sum',
            'InstanceID': 'count'
        }).round(2)
        
        account_project_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
        account_project_summary = account_project_summary.sort_values('含税总金额', ascending=False)
        
        print(account_project_summary.head(20))  # 显示前20条
        
        # 保存到CSV
        output_file = 'account_project_split.csv'
        account_project_summary.to_csv(output_file, encoding='utf-8-sig')
        print(f"\n账户-项目拆分结果已保存到: {output_file}")
        
        return account_project_summary
    
    def generate_detailed_report(self):
        """生成详细报告"""
        print("\n=== 生成详细分析报告 ===")
        
        # 创建报告文件
        report_file = f"bill_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("腾讯云账单分析报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"数据文件: {self.csv_file_path}\n\n")
            
            # 总体统计
            stats = self.get_summary_stats()
            f.write("总体统计:\n")
            f.write(f"  总记录数: {stats['total_records']:,}\n")
            f.write(f"  含税总金额: ${stats['total_cost_with_tax']:,.2f}\n")
            f.write(f"  税前总金额: ${stats['total_before_tax']:,.2f}\n")
            f.write(f"  税费总额: ${stats['total_tax']:,.2f}\n\n")
            
            # 按账户统计
            f.write("按账户统计 (前10名):\n")
            account_summary = self.split_by_account()
            f.write(account_summary.head(10).to_string())
            f.write("\n\n")
            
            # 按项目统计
            f.write("按项目统计 (前10名):\n")
            project_summary = self.split_by_project()
            f.write(project_summary.head(10).to_string())
            f.write("\n\n")
            
            # 按产品统计
            f.write("按产品统计:\n")
            product_summary = self.split_by_product()
            f.write(product_summary.to_string())
            f.write("\n")
        
        print(f"详细报告已保存到: {report_file}")
    
    def run_all_analysis(self):
        """运行所有分析"""
        print("开始账单拆分分析...")
        
        # 总体统计
        self.get_summary_stats()
        
        # 各种拆分
        self.split_by_account()
        self.split_by_project()
        self.split_by_product()
        self.split_by_account_and_project()
        
        # 生成报告
        self.generate_detailed_report()
        
        print("\n所有分析完成！")

def main():
    """主函数"""
    # 查找CSV文件
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv')]
    
    if not csv_files:
        print("当前目录下没有找到CSV文件")
        return
    
    if len(csv_files) == 1:
        csv_file = csv_files[0]
        print(f"找到CSV文件: {csv_file}")
    else:
        print("找到多个CSV文件:")
        for i, file in enumerate(csv_files, 1):
            print(f"{i}. {file}")
        
        try:
            choice = int(input("请选择要分析的文件编号: ")) - 1
            csv_file = csv_files[choice]
        except (ValueError, IndexError):
            print("无效选择，使用第一个文件")
            csv_file = csv_files[0]
    
    # 创建拆分器并运行分析
    splitter = BillSplitter(csv_file)
    splitter.run_all_analysis()

if __name__ == "__main__":
    main()
