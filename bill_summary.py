#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账单拆分结果汇总展示
"""

import pandas as pd
import os

def display_summary():
    """显示账单拆分汇总"""
    
    print("=" * 60)
    print("腾讯云账单拆分结果汇总")
    print("=" * 60)
    
    # 读取各个拆分结果文件
    files = {
        'account_split.csv': '按账户拆分',
        'project_split.csv': '按项目拆分', 
        'product_split.csv': '按产品拆分',
        'account_project_split.csv': '按账户-项目拆分'
    }
    
    for filename, title in files.items():
        if os.path.exists(filename):
            print(f"\n{title}:")
            print("-" * 40)
            
            df = pd.read_csv(filename)
            
            if filename == 'account_split.csv':
                # 显示前10个账户
                print("费用最高的前10个账户:")
                top_accounts = df.head(10)
                for _, row in top_accounts.iterrows():
                    print(f"  账户 {row.iloc[0]}: ${row['含税总金额']:,.2f} (记录数: {row['记录数']})")
                
                total_cost = df['含税总金额'].sum()
                print(f"\n  总计: ${total_cost:,.2f}")
                
            elif filename == 'project_split.csv':
                # 显示前10个项目
                print("费用最高的前10个项目:")
                top_projects = df.head(10)
                for _, row in top_projects.iterrows():
                    print(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f} (记录数: {row['记录数']})")
                
            elif filename == 'product_split.csv':
                # 显示前10个产品
                print("费用最高的前10个产品:")
                top_products = df.head(10)
                for _, row in top_products.iterrows():
                    print(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f} (记录数: {row['记录数']})")
                    
            elif filename == 'account_project_split.csv':
                # 显示前10个账户-项目组合
                print("费用最高的前10个账户-项目组合:")
                top_combinations = df.head(10)
                for _, row in top_combinations.iterrows():
                    print(f"  账户 {row.iloc[0]} - {row.iloc[1]}: ${row['含税总金额']:,.2f}")
    
    print("\n" + "=" * 60)
    print("关键发现:")
    print("=" * 60)
    
    # 读取账户拆分数据进行分析
    if os.path.exists('account_split.csv'):
        account_df = pd.read_csv('account_split.csv')
        
        # 找出费用最高的账户
        top_account = account_df.iloc[0]
        total_cost = account_df['含税总金额'].sum()
        top_account_percentage = (top_account['含税总金额'] / total_cost) * 100
        
        print(f"1. 费用最高的账户: {top_account.iloc[0]}")
        print(f"   - 费用: ${top_account['含税总金额']:,.2f}")
        print(f"   - 占总费用比例: {top_account_percentage:.1f}%")
        print(f"   - 记录数: {top_account['记录数']}")
        
        # 找出记录数最多的账户
        max_records_account = account_df.loc[account_df['记录数'].idxmax()]
        print(f"\n2. 记录数最多的账户: {max_records_account.iloc[0]}")
        print(f"   - 记录数: {max_records_account['记录数']}")
        print(f"   - 费用: ${max_records_account['含税总金额']:,.2f}")
    
    # 读取项目拆分数据进行分析
    if os.path.exists('project_split.csv'):
        project_df = pd.read_csv('project_split.csv')
        
        # 找出费用最高的项目
        top_project = project_df.iloc[0]
        total_project_cost = project_df['含税总金额'].sum()
        top_project_percentage = (top_project['含税总金额'] / total_project_cost) * 100
        
        print(f"\n3. 费用最高的项目: {top_project.iloc[0]}")
        print(f"   - 费用: ${top_project['含税总金额']:,.2f}")
        print(f"   - 占总费用比例: {top_project_percentage:.1f}%")
        print(f"   - 记录数: {top_project['记录数']}")
    
    # 读取产品拆分数据进行分析
    if os.path.exists('product_split.csv'):
        product_df = pd.read_csv('product_split.csv')
        
        # 找出费用最高的产品
        top_product = product_df.iloc[0]
        total_product_cost = product_df['含税总金额'].sum()
        top_product_percentage = (top_product['含税总金额'] / total_product_cost) * 100
        
        print(f"\n4. 费用最高的产品: {top_product.iloc[0]}")
        print(f"   - 费用: ${top_product['含税总金额']:,.2f}")
        print(f"   - 占总费用比例: {top_product_percentage:.1f}%")
        print(f"   - 记录数: {top_product['记录数']}")
    
    print("\n" + "=" * 60)
    print("建议:")
    print("=" * 60)
    print("1. 重点关注费用最高的账户和项目，优化资源使用")
    print("2. 检查'default'项目下的资源，考虑重新分类到具体项目")
    print("3. 分析CDN和EdgeOne的使用情况，这两项占据了大部分费用")
    print("4. 定期审查各账户的资源使用情况，避免资源浪费")
    print("5. 考虑使用预付费模式来降低部分产品的成本")

if __name__ == "__main__":
    display_summary()
