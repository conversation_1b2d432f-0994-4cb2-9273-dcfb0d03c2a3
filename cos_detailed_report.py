#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成COS桶的详细分析报告
"""

import pandas as pd
import os
from datetime import datetime

def generate_cos_detailed_report():
    """生成COS桶的详细分析报告"""
    
    print("=" * 80)
    print("COS桶消费情况详细分析报告")
    print("=" * 80)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 读取COS相关数据
    files_to_check = {
        'cos_bucket_summary.csv': '桶统计',
        'cos_service_summary.csv': '服务类型统计',
        'cos_account_summary.csv': '账户统计',
        'cos_detailed_data.csv': '详细数据'
    }
    
    for filename, description in files_to_check.items():
        if not os.path.exists(filename):
            print(f"文件 {filename} 不存在，请先运行 cos_bucket_analysis.py")
            return
    
    # 读取桶统计数据
    bucket_df = pd.read_csv('cos_bucket_summary.csv')
    service_df = pd.read_csv('cos_service_summary.csv')
    account_df = pd.read_csv('cos_account_summary.csv')
    detailed_df = pd.read_csv('cos_detailed_data.csv')
    
    # 总体统计
    total_cost = bucket_df['含税总金额'].sum()
    total_buckets = len(bucket_df)
    
    print(f"\n总体概览:")
    print("-" * 50)
    print(f"COS桶总数: {total_buckets}")
    print(f"COS总费用: ${total_cost:,.2f}")
    print(f"平均每桶费用: ${total_cost/total_buckets:,.2f}")
    
    # 费用分布分析
    print(f"\n费用分布分析:")
    print("-" * 50)
    
    # 按费用区间分类
    high_cost_buckets = bucket_df[bucket_df['含税总金额'] >= 1000]
    medium_cost_buckets = bucket_df[(bucket_df['含税总金额'] >= 100) & (bucket_df['含税总金额'] < 1000)]
    low_cost_buckets = bucket_df[(bucket_df['含税总金额'] >= 10) & (bucket_df['含税总金额'] < 100)]
    minimal_cost_buckets = bucket_df[bucket_df['含税总金额'] < 10]
    
    print(f"高费用桶 (≥$1000): {len(high_cost_buckets)}个，总费用: ${high_cost_buckets['含税总金额'].sum():,.2f}")
    print(f"中费用桶 ($100-$999): {len(medium_cost_buckets)}个，总费用: ${medium_cost_buckets['含税总金额'].sum():,.2f}")
    print(f"低费用桶 ($10-$99): {len(low_cost_buckets)}个，总费用: ${low_cost_buckets['含税总金额'].sum():,.2f}")
    print(f"微费用桶 (<$10): {len(minimal_cost_buckets)}个，总费用: ${minimal_cost_buckets['含税总金额'].sum():,.2f}")
    
    # 高费用桶详细分析
    print(f"\n高费用桶详细分析 (≥$1000):")
    print("-" * 50)
    
    if len(high_cost_buckets) > 0:
        for _, bucket in high_cost_buckets.iterrows():
            percentage = (bucket['含税总金额'] / total_cost) * 100
            print(f"  {bucket['bucket_name']}: ${bucket['含税总金额']:,.2f} ({percentage:.1f}%)")
            
            # 查找该桶的详细信息
            bucket_details = detailed_df[detailed_df['bucket_name'] == bucket['bucket_name']]
            if len(bucket_details) > 0:
                account = bucket_details['Owner Account ID'].iloc[0]
                region = bucket_details['Region'].iloc[0]
                print(f"    - 账户: {account}")
                print(f"    - 区域: {region}")
                print(f"    - 记录数: {bucket['记录数']}")
                
                # 分析存储类型
                storage_types = bucket_details['service_type'].value_counts()
                for storage_type, count in storage_types.items():
                    print(f"    - {storage_type}: {count}条记录")
                print()
    else:
        print("  没有费用超过$1000的桶")
    
    # 按账户分析桶分布
    print(f"\n按账户分析桶分布:")
    print("-" * 50)
    
    for _, account in account_df.iterrows():
        account_id = account['Owner Account ID']
        account_buckets = detailed_df[detailed_df['Owner Account ID'] == account_id]['bucket_name'].unique()
        percentage = (account['含税总金额'] / total_cost) * 100
        
        print(f"账户 {account_id}: ${account['含税总金额']:,.2f} ({percentage:.1f}%)")
        print(f"  - 桶数量: {len(account_buckets)}")
        print(f"  - 平均每桶费用: ${account['含税总金额']/len(account_buckets):,.2f}")
        
        # 显示该账户费用最高的3个桶
        account_bucket_costs = []
        for bucket_name in account_buckets:
            bucket_cost = bucket_df[bucket_df['bucket_name'] == bucket_name]['含税总金额'].iloc[0]
            account_bucket_costs.append((bucket_name, bucket_cost))
        
        account_bucket_costs.sort(key=lambda x: x[1], reverse=True)
        print(f"  - 费用最高的桶:")
        for i, (bucket_name, cost) in enumerate(account_bucket_costs[:3]):
            print(f"    {i+1}. {bucket_name}: ${cost:,.2f}")
        print()
    
    # 服务类型分析
    print(f"\n服务类型分析:")
    print("-" * 50)
    
    for _, service in service_df.iterrows():
        service_type = service['service_type']
        percentage = (service['含税总金额'] / total_cost) * 100
        print(f"{service_type}: ${service['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 区域分析
    print(f"\n区域分析:")
    print("-" * 50)
    
    region_summary = detailed_df.groupby('Region')['Total Cost (Including Tax)'].sum().sort_values(ascending=False)
    
    for region, cost in region_summary.items():
        percentage = (cost / total_cost) * 100
        region_buckets = detailed_df[detailed_df['Region'] == region]['bucket_name'].nunique()
        print(f"{region}: ${cost:,.2f} ({percentage:.1f}%) - {region_buckets}个桶")
    
    # 优化建议
    print(f"\n" + "="*80)
    print("COS优化建议:")
    print("="*80)
    
    # 基于分析结果给出建议
    if len(high_cost_buckets) > 0:
        print("1. 高费用桶优化:")
        for _, bucket in high_cost_buckets.head(3).iterrows():
            print(f"   - {bucket['bucket_name']}: 考虑使用生命周期管理，将旧数据转为低频或归档存储")
    
    storage_cost = service_df[service_df['service_type'] == '标准存储']['含税总金额'].iloc[0]
    traffic_cost = service_df[service_df['service_type'] == '流量费用']['含税总金额'].iloc[0] if len(service_df[service_df['service_type'] == '流量费用']) > 0 else 0
    
    if traffic_cost > 0:
        traffic_percentage = (traffic_cost / total_cost) * 100
        print(f"\n2. 流量费用优化:")
        print(f"   - 流量费用占比: {traffic_percentage:.1f}%")
        if traffic_percentage > 10:
            print(f"   - 建议: 优化CDN配置，减少回源流量")
            print(f"   - 建议: 检查是否有不必要的跨区域数据传输")
    
    print(f"\n3. 存储策略优化:")
    print(f"   - 标准存储费用: ${storage_cost:,.2f}")
    print(f"   - 建议: 分析数据访问频率，将冷数据转为低频存储")
    print(f"   - 建议: 设置生命周期规则，自动转换存储类型")
    
    if len(minimal_cost_buckets) > 10:
        print(f"\n4. 桶管理优化:")
        print(f"   - 发现 {len(minimal_cost_buckets)} 个微费用桶 (<$10)")
        print(f"   - 建议: 考虑合并或清理不必要的桶")
        print(f"   - 建议: 定期审查桶的使用情况")
    
    # 生成详细报告文件
    report_filename = f"cos_detailed_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("COS桶消费情况详细分析报告\n")
        f.write("=" * 80 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("总体概览:\n")
        f.write("-" * 50 + "\n")
        f.write(f"COS桶总数: {total_buckets}\n")
        f.write(f"COS总费用: ${total_cost:,.2f}\n")
        f.write(f"平均每桶费用: ${total_cost/total_buckets:,.2f}\n\n")
        
        f.write("所有桶费用明细:\n")
        f.write("-" * 50 + "\n")
        for _, bucket in bucket_df.iterrows():
            percentage = (bucket['含税总金额'] / total_cost) * 100
            f.write(f"{bucket['bucket_name']}: ${bucket['含税总金额']:,.2f} ({percentage:.1f}%)\n")
        
        f.write(f"\n按账户统计:\n")
        f.write("-" * 50 + "\n")
        for _, account in account_df.iterrows():
            percentage = (account['含税总金额'] / total_cost) * 100
            f.write(f"账户 {account['Owner Account ID']}: ${account['含税总金额']:,.2f} ({percentage:.1f}%)\n")
        
        f.write(f"\n按服务类型统计:\n")
        f.write("-" * 50 + "\n")
        for _, service in service_df.iterrows():
            percentage = (service['含税总金额'] / total_cost) * 100
            f.write(f"{service['service_type']}: ${service['含税总金额']:,.2f} ({percentage:.1f}%)\n")
    
    print(f"\n✓ 详细报告已保存到: {report_filename}")
    
    print(f"\n总结:")
    print("-" * 50)
    print(f"• 共分析了 {total_buckets} 个COS桶")
    print(f"• 总费用 ${total_cost:,.2f}")
    print(f"• 前4个桶占总费用的 {(high_cost_buckets['含税总金额'].sum()/total_cost)*100:.1f}%")
    print(f"• 主要费用来源是标准存储，建议优化存储策略")

if __name__ == "__main__":
    generate_cos_detailed_report()
