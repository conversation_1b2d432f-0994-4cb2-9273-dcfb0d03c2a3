#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单独分析CDN和EdgeOne的使用情况
"""

import pandas as pd
import os

def analyze_cdn_edgeone():
    """单独分析CDN和EdgeOne的费用"""
    
    # 读取原始数据
    original_file = "L1-consumption_bill_by_instance-200033549284-202505-part.0.csv"
    
    if not os.path.exists(original_file):
        print(f"原始文件 {original_file} 不存在")
        return
    
    print("正在读取原始账单数据...")
    df = pd.read_csv(original_file)
    
    # 转换数值列
    numeric_columns = [
        'Total Cost', 'Total Amount After Discount (Excluding Tax)',
        'Amount Before Tax', 'TaxAmount', 'Total Cost (Including Tax)'
    ]
    
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 筛选CDN和EdgeOne数据
    cdn_edgeone_products = ['Content Delivery Network', 'EdgeOne']
    cdn_edgeone_df = df[df['ProductName'].isin(cdn_edgeone_products)]
    
    print(f"CDN和EdgeOne总记录数: {len(cdn_edgeone_df)} 条")
    
    if len(cdn_edgeone_df) == 0:
        print("没有找到CDN和EdgeOne的记录")
        return
    
    # 保存CDN和EdgeOne原始数据
    cdn_edgeone_file = 'cdn_edgeone_data.csv'
    cdn_edgeone_df.to_csv(cdn_edgeone_file, index=False, encoding='utf-8-sig')
    print(f"✓ CDN和EdgeOne原始数据已保存到: {cdn_edgeone_file}")
    
    # 统计CDN和EdgeOne总体情况
    print("\n" + "="*60)
    print("CDN和EdgeOne统计:")
    print("="*60)
    
    total_before_tax = cdn_edgeone_df['Amount Before Tax'].sum()
    total_tax = cdn_edgeone_df['TaxAmount'].sum()
    total_with_tax = cdn_edgeone_df['Total Cost (Including Tax)'].sum()
    
    print(f"记录数: {len(cdn_edgeone_df):,}")
    print(f"税前金额: ${total_before_tax:,.2f}")
    print(f"税费: ${total_tax:,.2f}")
    print(f"含税总金额: ${total_with_tax:,.2f}")
    
    # 按产品分类统计
    product_summary = cdn_edgeone_df.groupby('ProductName').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    product_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    product_summary = product_summary.sort_values('含税总金额', ascending=False)
    
    print(f"\n按产品分类:")
    print("-" * 40)
    for product, row in product_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {product}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 保存CDN和EdgeOne产品统计
    cdn_edgeone_summary_file = 'cdn_edgeone_summary.csv'
    product_summary.to_csv(cdn_edgeone_summary_file, encoding='utf-8-sig')
    print(f"\n✓ CDN和EdgeOne产品统计已保存到: {cdn_edgeone_summary_file}")
    
    # 按账户分类统计
    account_summary = cdn_edgeone_df.groupby('Owner Account ID').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    account_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    account_summary = account_summary.sort_values('含税总金额', ascending=False)
    
    print(f"\n按账户分类:")
    print("-" * 40)
    for account, row in account_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  账户 {account}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 保存CDN和EdgeOne账户统计
    cdn_edgeone_account_file = 'cdn_edgeone_account_summary.csv'
    account_summary.to_csv(cdn_edgeone_account_file, encoding='utf-8-sig')
    print(f"\n✓ CDN和EdgeOne账户统计已保存到: {cdn_edgeone_account_file}")
    
    # 按项目分类统计
    project_summary = cdn_edgeone_df.groupby('ProjectName').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    project_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    project_summary = project_summary.sort_values('含税总金额', ascending=False)
    
    print(f"\n按项目分类 (前10名):")
    print("-" * 40)
    for project, row in project_summary.head(10).iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {project}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 保存CDN和EdgeOne项目统计
    cdn_edgeone_project_file = 'cdn_edgeone_project_summary.csv'
    project_summary.to_csv(cdn_edgeone_project_file, encoding='utf-8-sig')
    print(f"\n✓ CDN和EdgeOne项目统计已保存到: {cdn_edgeone_project_file}")
    
    print(f"\nCDN和EdgeOne分析完成！")

if __name__ == "__main__":
    analyze_cdn_edgeone()
