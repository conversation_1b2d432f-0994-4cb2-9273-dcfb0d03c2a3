#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CVM实例详细分析
"""

import pandas as pd
import os
import re

def analyze_cvm_instances():
    """详细分析CVM实例的费用情况"""
    
    # 读取CVM、CBS、IP详细数据
    input_file = "cvm_cbs_ip_detailed_data.csv"
    
    if not os.path.exists(input_file):
        print(f"输入文件 {input_file} 不存在，请先运行 cvm_cbs_ip_analysis.py")
        return
    
    print("正在读取CVM、CBS、IP数据...")
    df = pd.read_csv(input_file)
    
    # 筛选CVM数据
    cvm_df = df[df['product_short'] == 'CVM'].copy()
    
    print(f"CVM记录数: {len(cvm_df)} 条")
    
    if len(cvm_df) == 0:
        print("没有找到CVM相关的记录")
        return
    
    # 转换数值列
    numeric_columns = ['Amount Before Tax', 'TaxAmount', 'Total Cost (Including Tax)']
    for col in numeric_columns:
        if col in cvm_df.columns:
            cvm_df[col] = pd.to_numeric(cvm_df[col], errors='coerce')
    
    def extract_instance_info(row):
        """从配置描述中提取实例信息"""
        config_desc = str(row['Configuration Description'])
        instance_id = str(row['InstanceID'])
        instance_name = str(row['InstanceName'])
        
        # 提取实例规格信息
        instance_type = "未知规格"
        cpu_cores = "未知"
        memory = "未知"
        
        # 尝试从配置描述中提取规格信息
        if pd.notna(config_desc) and config_desc != '\\N':
            # 查找CPU核数
            cpu_match = re.search(r'(\d+)\s*[Cc]ore', config_desc)
            if cpu_match:
                cpu_cores = cpu_match.group(1) + "核"
            
            # 查找内存大小
            memory_match = re.search(r'(\d+(?:\.\d+)?)\s*[Gg][Bb]', config_desc)
            if memory_match:
                memory = memory_match.group(1) + "GB"
            
            # 查找实例类型
            if 'S5' in config_desc:
                instance_type = "S5标准型"
            elif 'S4' in config_desc:
                instance_type = "S4标准型"
            elif 'S3' in config_desc:
                instance_type = "S3标准型"
            elif 'C4' in config_desc:
                instance_type = "C4计算型"
            elif 'M5' in config_desc:
                instance_type = "M5内存型"
            elif 'I3' in config_desc:
                instance_type = "I3高IO型"
        
        return instance_type, cpu_cores, memory
    
    # 提取实例信息
    print("正在分析实例配置信息...")
    cvm_df[['instance_type', 'cpu_cores', 'memory']] = cvm_df.apply(
        lambda row: pd.Series(extract_instance_info(row)), axis=1
    )
    
    # 保存CVM详细数据
    cvm_detail_file = 'cvm_detailed_analysis.csv'
    cvm_df.to_csv(cvm_detail_file, index=False, encoding='utf-8-sig')
    print(f"✓ CVM详细分析数据已保存到: {cvm_detail_file}")
    
    # CVM总体统计
    print("\n" + "="*60)
    print("CVM总体统计:")
    print("="*60)
    
    total_before_tax = cvm_df['Amount Before Tax'].sum()
    total_tax = cvm_df['TaxAmount'].sum()
    total_with_tax = cvm_df['Total Cost (Including Tax)'].sum()
    
    print(f"CVM实例记录数: {len(cvm_df):,}")
    print(f"税前金额: ${total_before_tax:,.2f}")
    print(f"税费: ${total_tax:,.2f}")
    print(f"含税总金额: ${total_with_tax:,.2f}")
    
    # 按账户统计CVM
    print(f"\n按账户统计CVM费用:")
    print("-" * 50)
    
    account_summary = cvm_df.groupby('Owner Account ID').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    account_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    account_summary = account_summary.sort_values('含税总金额', ascending=False)
    
    for account, row in account_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  账户 {account}: ${row['含税总金额']:,.2f} ({percentage:.1f}%) - {row['记录数']}条记录")
    
    # 按实例类型统计
    print(f"\n按实例类型统计:")
    print("-" * 50)
    
    type_summary = cvm_df.groupby('instance_type').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    type_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    type_summary = type_summary.sort_values('含税总金额', ascending=False)
    
    for instance_type, row in type_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {instance_type}: ${row['含税总金额']:,.2f} ({percentage:.1f}%) - {row['记录数']}条记录")
    
    # 按项目统计CVM
    print(f"\n按项目统计CVM费用:")
    print("-" * 50)
    
    project_summary = cvm_df.groupby('ProjectName').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    project_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    project_summary = project_summary.sort_values('含税总金额', ascending=False)
    
    print("费用最高的前10个项目:")
    for project, row in project_summary.head(10).iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {project}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 按区域统计CVM
    print(f"\n按区域统计CVM费用:")
    print("-" * 50)
    
    region_summary = cvm_df.groupby('Region').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    region_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    region_summary = region_summary.sort_values('含税总金额', ascending=False)
    
    for region, row in region_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {region}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 分析高费用实例
    print(f"\n高费用CVM实例分析:")
    print("-" * 50)
    
    # 按实例ID分组统计
    instance_summary = cvm_df.groupby('InstanceID').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'Owner Account ID': 'first',
        'ProjectName': 'first',
        'Region': 'first',
        'instance_type': 'first',
        'cpu_cores': 'first',
        'memory': 'first'
    }).round(2)
    
    instance_summary.columns = ['含税总金额', '税前金额', '税费', '账户ID', '项目', '区域', '实例类型', 'CPU', '内存']
    instance_summary = instance_summary.sort_values('含税总金额', ascending=False)
    
    print("费用最高的前10个CVM实例:")
    for instance_id, row in instance_summary.head(10).iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {instance_id}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
        print(f"    - 账户: {row['账户ID']}, 项目: {row['项目']}")
        print(f"    - 区域: {row['区域']}")
        print(f"    - 规格: {row['实例类型']}, CPU: {row['CPU']}, 内存: {row['内存']}")
        print()
    
    # 保存各种统计结果
    account_summary.to_csv('cvm_account_summary.csv', encoding='utf-8-sig')
    type_summary.to_csv('cvm_type_summary.csv', encoding='utf-8-sig')
    project_summary.to_csv('cvm_project_summary.csv', encoding='utf-8-sig')
    region_summary.to_csv('cvm_region_summary.csv', encoding='utf-8-sig')
    instance_summary.to_csv('cvm_instance_summary.csv', encoding='utf-8-sig')
    
    print(f"优化建议:")
    print("-" * 50)
    print("1. 重点关注费用最高的实例，检查是否可以降配")
    print("2. 分析实例的CPU和内存使用率，优化配置")
    print("3. 考虑使用预付费模式降低成本")
    print("4. 定期审查各项目的实例分配")
    print("5. 考虑跨区域成本差异，优化部署策略")
    
    print(f"\n生成的CVM分析文件:")
    print("-" * 40)
    files = [
        'cvm_detailed_analysis.csv - CVM详细分析数据',
        'cvm_account_summary.csv - CVM账户统计',
        'cvm_type_summary.csv - CVM实例类型统计',
        'cvm_project_summary.csv - CVM项目统计',
        'cvm_region_summary.csv - CVM区域统计',
        'cvm_instance_summary.csv - CVM实例统计'
    ]
    
    for file_desc in files:
        print(f"  ✓ {file_desc}")

if __name__ == "__main__":
    analyze_cvm_instances()
