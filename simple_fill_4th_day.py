#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单填充4日目和集計数据
"""

import pandas as pd
import numpy as np
import os

def simple_fill_xlsx():
    """简单填充xlsx文件的4日目和集計数据"""
    
    xlsx_file = "【問題資料】店舗別売上集計表.xlsx"
    
    if not os.path.exists(xlsx_file):
        print(f"文件 {xlsx_file} 不存在")
        return
    
    print("读取Excel文件...")
    
    # 读取所有相关工作表
    day1_df = pd.read_excel(xlsx_file, sheet_name='1日目')
    day2_df = pd.read_excel(xlsx_file, sheet_name='2日目')
    day3_df = pd.read_excel(xlsx_file, sheet_name='3日目')
    day4_df = pd.read_excel(xlsx_file, sheet_name='4日目')
    summary_df = pd.read_excel(xlsx_file, sheet_name='集計')
    
    print("分析数据结构...")
    print(f"1日目数据: {day1_df.shape}")
    print(f"4日目数据: {day4_df.shape}")
    print(f"集計数据: {summary_df.shape}")
    
    # 显示1日目的数据结构作为参考
    print(f"\n1日目数据样本:")
    print(day1_df.head())
    
    # 填充4日目数据
    print(f"\n开始填充4日目数据...")
    filled_day4 = fill_day4_simple(day1_df, day2_df, day3_df, day4_df)
    
    # 填充集計数据
    print(f"\n开始填充集計数据...")
    filled_summary = fill_summary_simple(day1_df, day2_df, day3_df, filled_day4, summary_df)
    
    # 保存结果
    output_file = "simple_filled_result.xlsx"
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 保存原始数据
        day1_df.to_excel(writer, sheet_name='1日目', index=False)
        day2_df.to_excel(writer, sheet_name='2日目', index=False)
        day3_df.to_excel(writer, sheet_name='3日目', index=False)
        
        # 保存填充后的数据
        filled_day4.to_excel(writer, sheet_name='4日目', index=False)
        filled_summary.to_excel(writer, sheet_name='集計', index=False)
    
    print(f"\n✓ 填充完成，结果保存到: {output_file}")
    
    # 显示填充结果
    print(f"\n4日目填充结果预览:")
    print(filled_day4.head(10))
    
    print(f"\n集計填充结果预览:")
    print(filled_summary.head(10))

def fill_day4_simple(day1_df, day2_df, day3_df, day4_df):
    """简单填充4日目数据"""
    
    # 复制4日目数据框
    result_df = day4_df.copy()
    
    # 找到数据开始的行（跳过标题）
    data_start_row = 1  # 假设第2行开始是数据
    
    # 从前3天提取对应行的数据
    for i in range(data_start_row, len(result_df)):
        if i < len(day1_df) and i < len(day2_df) and i < len(day3_df):
            
            # 提取前3天对应行的数值数据（假设从第4列开始是数值）
            day1_values = []
            day2_values = []
            day3_values = []
            
            # 提取数值列（弁当、おにぎり、惣菜）
            for col_idx in range(3, 6):  # 假设第4-6列是商品销售数据
                if col_idx < len(day1_df.columns):
                    val1 = day1_df.iloc[i, col_idx]
                    val2 = day2_df.iloc[i, col_idx]
                    val3 = day3_df.iloc[i, col_idx]
                    
                    # 转换为数值
                    val1 = pd.to_numeric(val1, errors='coerce') if pd.notna(val1) else 0
                    val2 = pd.to_numeric(val2, errors='coerce') if pd.notna(val2) else 0
                    val3 = pd.to_numeric(val3, errors='coerce') if pd.notna(val3) else 0
                    
                    day1_values.append(val1)
                    day2_values.append(val2)
                    day3_values.append(val3)
            
            # 使用简单的预测函数填充4日目
            if day1_values and day2_values and day3_values:
                for j, (v1, v2, v3) in enumerate(zip(day1_values, day2_values, day3_values)):
                    col_idx = j + 3
                    if col_idx < len(result_df.columns):
                        # 使用简单的平均值预测
                        predicted_value = predict_next_day_value(v1, v2, v3)
                        result_df.iloc[i, col_idx] = predicted_value
                
                # 计算合计（最后一列）
                if len(result_df.columns) > 6:
                    total = sum(pd.to_numeric(result_df.iloc[i, col], errors='coerce') or 0 
                              for col in range(3, 6))
                    result_df.iloc[i, 6] = total
    
    return result_df

def predict_next_day_value(val1, val2, val3):
    """预测下一天的值的简单函数"""
    
    # 如果所有值都是0，返回0
    if val1 == 0 and val2 == 0 and val3 == 0:
        return 0
    
    # 方法1: 简单平均
    avg = (val1 + val2 + val3) / 3
    
    # 方法2: 趋势预测
    if val2 != 0:
        trend = (val3 - val2) / val2
        trend_prediction = val3 * (1 + trend)
    else:
        trend_prediction = val3
    
    # 方法3: 加权平均（近期权重更高）
    weighted_avg = (val1 * 0.2 + val2 * 0.3 + val3 * 0.5)
    
    # 综合预测（取平均）
    final_prediction = (avg + trend_prediction + weighted_avg) / 3
    
    # 确保结果为正数且合理
    return max(0, round(final_prediction))

def fill_summary_simple(day1_df, day2_df, day3_df, day4_df, summary_df):
    """简单填充集計数据"""
    
    result_df = summary_df.copy()
    
    print("分析集計表结构...")
    print(f"集計表形状: {summary_df.shape}")
    print(f"集計表列名: {list(summary_df.columns)}")
    print(f"集計表前5行:")
    print(summary_df.head())
    
    # 如果集計表是汇总各天数据的，我们需要计算每个店舗4天的总计
    data_start_row = 1
    
    for i in range(data_start_row, len(result_df)):
        if i < len(day1_df) and i < len(day4_df):
            
            # 计算4天的汇总
            for col_idx in range(3, min(7, len(result_df.columns))):  # 商品列和合计列
                
                # 从4天数据中提取对应值
                val1 = get_numeric_value(day1_df, i, col_idx)
                val2 = get_numeric_value(day2_df, i, col_idx)
                val3 = get_numeric_value(day3_df, i, col_idx)
                val4 = get_numeric_value(day4_df, i, col_idx)
                
                # 计算4天总计
                total_4_days = val1 + val2 + val3 + val4
                
                # 填充到集計表
                result_df.iloc[i, col_idx] = total_4_days
    
    return result_df

def get_numeric_value(df, row_idx, col_idx):
    """安全获取数值"""
    if row_idx < len(df) and col_idx < len(df.columns):
        val = df.iloc[row_idx, col_idx]
        return pd.to_numeric(val, errors='coerce') or 0
    return 0

def create_simple_functions():
    """创建简单的填充函数示例"""
    
    functions = {
        'average': lambda x, y, z: (x + y + z) / 3,
        'trend': lambda x, y, z: z + (z - y) if y != 0 else z,
        'weighted': lambda x, y, z: x * 0.2 + y * 0.3 + z * 0.5,
        'growth': lambda x, y, z: z * 1.05,  # 假设5%增长
        'seasonal': lambda x, y, z: (x + y + z) / 3 * 0.95,  # 假设季节性下降
    }
    
    return functions

if __name__ == "__main__":
    print("=" * 60)
    print("简单填充4日目和集計数据")
    print("=" * 60)
    
    # 显示可用的简单函数
    functions = create_simple_functions()
    print("可用的预测函数:")
    for name, func in functions.items():
        print(f"  {name}: {func.__doc__ or '简单预测函数'}")
    
    print(f"\n开始处理...")
    simple_fill_xlsx()
    
    print(f"\n处理完成！")
    print("生成的文件: simple_filled_result.xlsx")
    print("包含填充后的4日目和集計数据")
