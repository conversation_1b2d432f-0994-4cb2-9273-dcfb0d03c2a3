#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CVM、CBS、IP综合分析报告
"""

import pandas as pd
import os
from datetime import datetime

def generate_comprehensive_cvm_cbs_ip_report():
    """生成CVM、CBS、IP的综合分析报告"""
    
    print("=" * 80)
    print("CVM、CBS、IP综合分析报告")
    print("=" * 80)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查必要文件是否存在
    required_files = [
        'cvm_cbs_ip_account_pivot_summary.csv',
        'cvm_cbs_ip_product_summary.csv',
        'cvm_instance_summary.csv'
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"文件 {file} 不存在，请先运行相关分析脚本")
            return
    
    # 读取数据
    account_pivot = pd.read_csv('cvm_cbs_ip_account_pivot_summary.csv')
    product_summary = pd.read_csv('cvm_cbs_ip_product_summary.csv')
    
    # 总体概览
    total_cost = account_pivot['总计'].sum()
    total_accounts = len(account_pivot)
    
    print(f"\n总体概览:")
    print("-" * 50)
    print(f"涉及账户数: {total_accounts}")
    print(f"CVM、CBS、IP总费用: ${total_cost:,.2f}")
    
    # 产品费用分布
    print(f"\n产品费用分布:")
    print("-" * 50)
    
    for _, product in product_summary.iterrows():
        product_name = product['product_short']
        cost = product['含税总金额']
        percentage = (cost / total_cost) * 100
        records = product['记录数']
        print(f"{product_name}: ${cost:,.2f} ({percentage:.1f}%) - {records}条记录")
    
    # 账户费用排名
    print(f"\n账户费用排名:")
    print("-" * 50)
    
    account_pivot_sorted = account_pivot.sort_values('总计', ascending=False)
    
    for _, account in account_pivot_sorted.iterrows():
        account_id = account['Owner Account ID']
        total = account['总计']
        cvm_cost = account['CVM']
        cbs_cost = account['CBS']
        ip_cost = account['IP']
        percentage = (total / total_cost) * 100
        
        print(f"账户 {account_id}: ${total:,.2f} ({percentage:.1f}%)")
        print(f"  - CVM: ${cvm_cost:,.2f}")
        print(f"  - CBS: ${cbs_cost:,.2f}")
        print(f"  - IP: ${ip_cost:,.2f}")
        print()
    
    # 费用集中度分析
    print(f"费用集中度分析:")
    print("-" * 50)
    
    top_account_cost = account_pivot_sorted.iloc[0]['总计']
    top_2_cost = account_pivot_sorted.head(2)['总计'].sum()
    top_3_cost = account_pivot_sorted.head(3)['总计'].sum()
    
    print(f"最大账户占比: {(top_account_cost/total_cost)*100:.1f}%")
    print(f"前2个账户占比: {(top_2_cost/total_cost)*100:.1f}%")
    print(f"前3个账户占比: {(top_3_cost/total_cost)*100:.1f}%")
    
    # CVM实例分析（如果文件存在）
    if os.path.exists('cvm_instance_summary.csv'):
        print(f"\nCVM高费用实例分析:")
        print("-" * 50)
        
        cvm_instances = pd.read_csv('cvm_instance_summary.csv')
        cvm_total = product_summary[product_summary['product_short'] == 'CVM']['含税总金额'].iloc[0]
        
        print(f"CVM实例总数: {len(cvm_instances)}")
        print(f"CVM总费用: ${cvm_total:,.2f}")
        print(f"平均每实例费用: ${cvm_total/len(cvm_instances):,.2f}")
        
        print(f"\n费用最高的5个CVM实例:")
        for _, instance in cvm_instances.head(5).iterrows():
            instance_id = instance['InstanceID']
            cost = instance['含税总金额']
            account = instance['账户ID']
            project = instance['项目']
            percentage = (cost / cvm_total) * 100
            
            print(f"  {instance_id}: ${cost:,.2f} ({percentage:.1f}%)")
            print(f"    账户: {account}, 项目: {project}")
    
    # 区域分布分析（如果文件存在）
    if os.path.exists('cvm_region_summary.csv'):
        print(f"\nCVM区域分布分析:")
        print("-" * 50)
        
        region_summary = pd.read_csv('cvm_region_summary.csv')
        cvm_total = product_summary[product_summary['product_short'] == 'CVM']['含税总金额'].iloc[0]
        
        print(f"费用最高的5个区域:")
        for _, region in region_summary.head(5).iterrows():
            region_name = region['Region']
            cost = region['含税总金额']
            percentage = (cost / cvm_total) * 100
            
            print(f"  {region_name}: ${cost:,.2f} ({percentage:.1f}%)")
    
    # 项目分布分析（如果文件存在）
    if os.path.exists('cvm_project_summary.csv'):
        print(f"\nCVM项目分布分析:")
        print("-" * 50)
        
        project_summary_cvm = pd.read_csv('cvm_project_summary.csv')
        cvm_total = product_summary[product_summary['product_short'] == 'CVM']['含税总金额'].iloc[0]
        
        print(f"费用最高的5个项目:")
        for _, project in project_summary_cvm.head(5).iterrows():
            project_name = project['ProjectName']
            cost = project['含税总金额']
            percentage = (cost / cvm_total) * 100
            
            print(f"  {project_name}: ${cost:,.2f} ({percentage:.1f}%)")
    
    # 优化建议
    print(f"\n" + "="*80)
    print("优化建议:")
    print("="*80)
    
    # 基于数据分析给出建议
    cvm_percentage = (product_summary[product_summary['product_short'] == 'CVM']['含税总金额'].iloc[0] / total_cost) * 100
    
    print(f"1. CVM优化 (占{cvm_percentage:.1f}%费用):")
    print(f"   - 重点关注费用最高的账户和实例")
    print(f"   - 分析实例的CPU和内存使用率")
    print(f"   - 考虑使用预付费模式降低成本")
    print(f"   - 定期审查实例规格是否合理")
    
    ip_percentage = (product_summary[product_summary['product_short'] == 'IP']['含税总金额'].iloc[0] / total_cost) * 100
    print(f"\n2. 公网IP优化 (占{ip_percentage:.1f}%费用):")
    print(f"   - 检查是否有闲置的公网IP")
    print(f"   - 优化带宽配置")
    print(f"   - 考虑使用NAT网关减少IP数量")
    
    cbs_percentage = (product_summary[product_summary['product_short'] == 'CBS']['含税总金额'].iloc[0] / total_cost) * 100
    print(f"\n3. 云硬盘优化 (占{cbs_percentage:.1f}%费用):")
    print(f"   - 清理不必要的存储卷")
    print(f"   - 优化存储类型配置")
    print(f"   - 定期清理快照")
    
    # 账户管理建议
    top_account_percentage = (top_account_cost/total_cost)*100
    if top_account_percentage > 70:
        print(f"\n4. 账户管理:")
        print(f"   - 最大账户占比过高({top_account_percentage:.1f}%)，建议分散风险")
        print(f"   - 考虑资源分配的合理性")
    
    # 生成详细报告文件
    report_filename = f"cvm_cbs_ip_comprehensive_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("CVM、CBS、IP综合分析报告\n")
        f.write("=" * 80 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("总体概览:\n")
        f.write("-" * 50 + "\n")
        f.write(f"涉及账户数: {total_accounts}\n")
        f.write(f"CVM、CBS、IP总费用: ${total_cost:,.2f}\n\n")
        
        f.write("产品费用分布:\n")
        f.write("-" * 50 + "\n")
        for _, product in product_summary.iterrows():
            product_name = product['product_short']
            cost = product['含税总金额']
            percentage = (cost / total_cost) * 100
            records = product['记录数']
            f.write(f"{product_name}: ${cost:,.2f} ({percentage:.1f}%) - {records}条记录\n")
        
        f.write(f"\n账户费用明细:\n")
        f.write("-" * 50 + "\n")
        for _, account in account_pivot_sorted.iterrows():
            account_id = account['Owner Account ID']
            total = account['总计']
            cvm_cost = account['CVM']
            cbs_cost = account['CBS']
            ip_cost = account['IP']
            percentage = (total / total_cost) * 100
            
            f.write(f"账户 {account_id}: ${total:,.2f} ({percentage:.1f}%)\n")
            f.write(f"  - CVM: ${cvm_cost:,.2f}\n")
            f.write(f"  - CBS: ${cbs_cost:,.2f}\n")
            f.write(f"  - IP: ${ip_cost:,.2f}\n\n")
    
    print(f"\n✓ 详细报告已保存到: {report_filename}")
    
    print(f"\n总结:")
    print("-" * 50)
    print(f"• CVM是最大的费用项，占{cvm_percentage:.1f}%")
    print(f"• 账户 {account_pivot_sorted.iloc[0]['Owner Account ID']} 费用最高，占{top_account_percentage:.1f}%")
    print(f"• 建议重点优化CVM实例配置和使用策略")
    print(f"• 定期审查资源使用情况，避免浪费")

if __name__ == "__main__":
    generate_comprehensive_cvm_cbs_ip_report()
