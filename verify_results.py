#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证填充结果
"""

import pandas as pd

def verify_filled_results():
    """验证填充结果"""
    
    print("=" * 60)
    print("验证填充结果")
    print("=" * 60)
    
    # 读取填充后的文件
    filled_file = "simple_filled_result.xlsx"
    
    try:
        # 读取各个工作表
        day1_df = pd.read_excel(filled_file, sheet_name='1日目')
        day2_df = pd.read_excel(filled_file, sheet_name='2日目')
        day3_df = pd.read_excel(filled_file, sheet_name='3日目')
        day4_df = pd.read_excel(filled_file, sheet_name='4日目')
        summary_df = pd.read_excel(filled_file, sheet_name='集計')
        
        print("✓ 成功读取所有工作表")
        
        # 显示4日目填充结果
        print(f"\n4日目填充结果 (前10行):")
        print("-" * 50)
        print(day4_df.head(10).to_string())
        
        # 显示集計填充结果
        print(f"\n集計填充结果 (前10行):")
        print("-" * 50)
        print(summary_df.head(10).to_string())
        
        # 验证数据一致性
        print(f"\n数据一致性验证:")
        print("-" * 50)
        
        # 检查第2行数据（第一个店舗）
        store_row = 1
        
        # 提取4天的数据
        day1_bento = pd.to_numeric(day1_df.iloc[store_row, 3], errors='coerce') or 0
        day2_bento = pd.to_numeric(day2_df.iloc[store_row, 3], errors='coerce') or 0
        day3_bento = pd.to_numeric(day3_df.iloc[store_row, 3], errors='coerce') or 0
        day4_bento = pd.to_numeric(day4_df.iloc[store_row, 3], errors='coerce') or 0
        
        summary_bento = pd.to_numeric(summary_df.iloc[store_row, 3], errors='coerce') or 0
        
        calculated_total = day1_bento + day2_bento + day3_bento + day4_bento
        
        print(f"店舗: {day1_df.iloc[store_row, 2]}")
        print(f"弁当销售额:")
        print(f"  1日目: ¥{day1_bento:,.0f}")
        print(f"  2日目: ¥{day2_bento:,.0f}")
        print(f"  3日目: ¥{day3_bento:,.0f}")
        print(f"  4日目: ¥{day4_bento:,.0f} (预测)")
        print(f"  计算总计: ¥{calculated_total:,.0f}")
        print(f"  集計表值: ¥{summary_bento:,.0f}")
        print(f"  差异: ¥{abs(calculated_total - summary_bento):,.0f}")
        
        # 统计所有店舗的4日目预测
        print(f"\n4日目预测统计:")
        print("-" * 50)
        
        # 提取所有店舗的4日目数据（跳过标题行）
        day4_data = []
        for i in range(1, min(len(day4_df), 20)):  # 假设最多20个店舗
            bento = pd.to_numeric(day4_df.iloc[i, 3], errors='coerce') or 0
            onigiri = pd.to_numeric(day4_df.iloc[i, 4], errors='coerce') or 0
            sozai = pd.to_numeric(day4_df.iloc[i, 5], errors='coerce') or 0
            total = pd.to_numeric(day4_df.iloc[i, 6], errors='coerce') or 0
            
            if total > 0:  # 只统计有效数据
                day4_data.append({
                    '店舗': day4_df.iloc[i, 2],
                    '弁当': bento,
                    'おにぎり': onigiri,
                    '惣菜': sozai,
                    '合計': total
                })
        
        if day4_data:
            day4_stats_df = pd.DataFrame(day4_data)
            
            print(f"有效店舗数: {len(day4_stats_df)}")
            print(f"4日目预测总销售额: ¥{day4_stats_df['合計'].sum():,.0f}")
            print(f"平均每店销售额: ¥{day4_stats_df['合計'].mean():,.0f}")
            
            print(f"\n各类商品预测:")
            print(f"弁当总额: ¥{day4_stats_df['弁当'].sum():,.0f}")
            print(f"おにぎり总额: ¥{day4_stats_df['おにぎり'].sum():,.0f}")
            print(f"惣菜总额: ¥{day4_stats_df['惣菜'].sum():,.0f}")
            
            # 显示销售额最高的5个店舗
            print(f"\n4日目销售额最高的5个店舗:")
            top5 = day4_stats_df.nlargest(5, '合計')
            for i, (_, row) in enumerate(top5.iterrows(), 1):
                print(f"{i}. {row['店舗']}: ¥{row['合計']:,.0f}")
        
        # 使用的预测函数说明
        print(f"\n" + "="*60)
        print("使用的预测函数:")
        print("="*60)
        print("1. 简单平均: (第1天 + 第2天 + 第3天) ÷ 3")
        print("2. 趋势预测: 第3天 + (第3天 - 第2天)")
        print("3. 加权平均: 第1天×0.2 + 第2天×0.3 + 第3天×0.5")
        print("4. 最终预测: 三种方法的平均值")
        print("5. 集計计算: 4天销售额的总和")
        
        print(f"\n✓ 验证完成！数据填充成功。")
        
    except Exception as e:
        print(f"验证时出错: {e}")

if __name__ == "__main__":
    verify_filled_results()
