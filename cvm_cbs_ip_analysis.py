#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取CVM、CBS、IP产品并按账号ID统计费用
"""

import pandas as pd
import os

def analyze_cvm_cbs_ip():
    """分析CVM、CBS、IP产品的费用情况"""
    
    # 读取剔除CDN和EdgeOne后的数据
    input_file = "bill_data_excluded_cdn_edgeone.csv"
    
    if not os.path.exists(input_file):
        print(f"输入文件 {input_file} 不存在")
        return
    
    print("正在读取账单数据...")
    df = pd.read_csv(input_file)
    
    # 转换数值列
    numeric_columns = [
        'Total Cost', 'Total Amount After Discount (Excluding Tax)',
        'Amount Before Tax', 'TaxAmount', 'Total Cost (Including Tax)'
    ]
    
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 定义产品映射
    product_mapping = {
        'Cloud Virtual Machine(CVM)': 'CVM',
        'cloud block storage': 'CBS', 
        'Cloud Public IP': 'IP'
    }
    
    # 筛选CVM、CBS、IP相关的记录
    target_products = list(product_mapping.keys())
    filtered_df = df[df['ProductName'].isin(target_products)].copy()
    
    print(f"CVM、CBS、IP总记录数: {len(filtered_df)} 条")
    
    if len(filtered_df) == 0:
        print("没有找到CVM、CBS、IP相关的记录")
        return
    
    # 添加产品简称列
    filtered_df['product_short'] = filtered_df['ProductName'].map(product_mapping)
    
    # 保存筛选后的详细数据
    detail_file = 'cvm_cbs_ip_detailed_data.csv'
    filtered_df.to_csv(detail_file, index=False, encoding='utf-8-sig')
    print(f"✓ CVM、CBS、IP详细数据已保存到: {detail_file}")
    
    # 总体统计
    print("\n" + "="*60)
    print("CVM、CBS、IP总体统计:")
    print("="*60)
    
    total_before_tax = filtered_df['Amount Before Tax'].sum()
    total_tax = filtered_df['TaxAmount'].sum()
    total_with_tax = filtered_df['Total Cost (Including Tax)'].sum()
    
    print(f"记录数: {len(filtered_df):,}")
    print(f"税前金额: ${total_before_tax:,.2f}")
    print(f"税费: ${total_tax:,.2f}")
    print(f"含税总金额: ${total_with_tax:,.2f}")
    
    # 按产品类型统计
    print(f"\n按产品类型统计:")
    print("-" * 50)
    
    product_summary = filtered_df.groupby('product_short').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    product_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    product_summary = product_summary.sort_values('含税总金额', ascending=False)
    
    for product, row in product_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {product}: ${row['含税总金额']:,.2f} ({percentage:.1f}%) - {row['记录数']}条记录")
    
    # 保存产品统计
    product_summary_file = 'cvm_cbs_ip_product_summary.csv'
    product_summary.to_csv(product_summary_file, encoding='utf-8-sig')
    print(f"\n✓ 产品统计已保存到: {product_summary_file}")
    
    # 按账号ID统计
    print(f"\n按账号ID统计:")
    print("-" * 50)
    
    account_summary = filtered_df.groupby('Owner Account ID').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    account_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    account_summary = account_summary.sort_values('含税总金额', ascending=False)
    
    for account, row in account_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  账户 {account}: ${row['含税总金额']:,.2f} ({percentage:.1f}%) - {row['记录数']}条记录")
    
    # 保存账户统计
    account_summary_file = 'cvm_cbs_ip_account_summary.csv'
    account_summary.to_csv(account_summary_file, encoding='utf-8-sig')
    print(f"\n✓ 账户统计已保存到: {account_summary_file}")
    
    # 按账号ID和产品类型双维度统计
    print(f"\n按账号ID和产品类型双维度统计:")
    print("-" * 50)
    
    account_product_summary = filtered_df.groupby(['Owner Account ID', 'product_short']).agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    account_product_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    account_product_summary = account_product_summary.sort_values('含税总金额', ascending=False)
    
    print("费用最高的前15个账户-产品组合:")
    for (account, product), row in account_product_summary.head(15).iterrows():
        print(f"  账户 {account} - {product}: ${row['含税总金额']:,.2f} ({row['记录数']}条记录)")
    
    # 保存账户-产品统计
    account_product_file = 'cvm_cbs_ip_account_product_summary.csv'
    account_product_summary.to_csv(account_product_file, encoding='utf-8-sig')
    print(f"\n✓ 账户-产品统计已保存到: {account_product_file}")
    
    # 详细分析每个账户的产品构成
    print(f"\n各账户产品构成详细分析:")
    print("-" * 50)
    
    for account in account_summary.index:
        account_data = filtered_df[filtered_df['Owner Account ID'] == account]
        account_total = account_data['Total Cost (Including Tax)'].sum()
        
        print(f"\n账户 {account} (总费用: ${account_total:,.2f}):")
        
        account_products = account_data.groupby('product_short')['Total Cost (Including Tax)'].sum().sort_values(ascending=False)
        
        for product, cost in account_products.items():
            percentage = (cost / account_total) * 100
            record_count = len(account_data[account_data['product_short'] == product])
            print(f"  - {product}: ${cost:,.2f} ({percentage:.1f}%) - {record_count}条记录")
    
    # 按项目统计
    print(f"\n按项目统计:")
    print("-" * 50)
    
    project_summary = filtered_df.groupby('ProjectName').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    project_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    project_summary = project_summary.sort_values('含税总金额', ascending=False)
    
    print("费用最高的前10个项目:")
    for project, row in project_summary.head(10).iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {project}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 保存项目统计
    project_summary_file = 'cvm_cbs_ip_project_summary.csv'
    project_summary.to_csv(project_summary_file, encoding='utf-8-sig')
    print(f"\n✓ 项目统计已保存到: {project_summary_file}")
    
    # 生成汇总表格 - 按账户显示各产品费用
    print(f"\n生成账户产品费用汇总表:")
    print("-" * 70)
    
    # 创建透视表
    pivot_table = filtered_df.pivot_table(
        values='Total Cost (Including Tax)',
        index='Owner Account ID',
        columns='product_short',
        aggfunc='sum',
        fill_value=0
    ).round(2)
    
    # 添加总计列
    pivot_table['总计'] = pivot_table.sum(axis=1)
    pivot_table = pivot_table.sort_values('总计', ascending=False)
    
    # 显示汇总表
    print(f"{'账户ID':<15} {'CVM':<12} {'CBS':<12} {'IP':<12} {'总计':<12}")
    print("-" * 70)
    
    for account in pivot_table.index:
        cvm_cost = pivot_table.loc[account, 'CVM'] if 'CVM' in pivot_table.columns else 0
        cbs_cost = pivot_table.loc[account, 'CBS'] if 'CBS' in pivot_table.columns else 0
        ip_cost = pivot_table.loc[account, 'IP'] if 'IP' in pivot_table.columns else 0
        total_cost = pivot_table.loc[account, '总计']
        
        print(f"{account:<15} ${cvm_cost:<11.2f} ${cbs_cost:<11.2f} ${ip_cost:<11.2f} ${total_cost:<11.2f}")
    
    # 保存汇总表
    pivot_summary_file = 'cvm_cbs_ip_account_pivot_summary.csv'
    pivot_table.to_csv(pivot_summary_file, encoding='utf-8-sig')
    print(f"\n✓ 账户产品费用汇总表已保存到: {pivot_summary_file}")
    
    print(f"\n" + "="*60)
    print("分析建议:")
    print("="*60)
    print("1. 重点关注费用最高的账户，优化资源配置")
    print("2. 检查CVM实例的规格是否合理，考虑降配或预付费")
    print("3. 审查CBS存储的使用情况，清理不必要的存储")
    print("4. 优化公网IP的使用，释放闲置IP")
    print("5. 定期审查各项目的资源分配")
    
    print(f"\n生成的分析文件:")
    print("-" * 40)
    files = [
        'cvm_cbs_ip_detailed_data.csv - 详细数据',
        'cvm_cbs_ip_product_summary.csv - 产品统计',
        'cvm_cbs_ip_account_summary.csv - 账户统计',
        'cvm_cbs_ip_account_product_summary.csv - 账户-产品统计',
        'cvm_cbs_ip_project_summary.csv - 项目统计',
        'cvm_cbs_ip_account_pivot_summary.csv - 账户产品费用汇总表'
    ]
    
    for file_desc in files:
        print(f"  ✓ {file_desc}")

if __name__ == "__main__":
    analyze_cvm_cbs_ip()
