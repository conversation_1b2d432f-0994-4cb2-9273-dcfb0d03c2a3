#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
区分客户使用和自己使用的账单
"""

import pandas as pd
import os

def classify_customer_vs_internal():
    """根据规则区分客户使用和内部使用的账单"""
    
    # 读取剔除CDN和EdgeOne后的数据
    input_file = "bill_data_excluded_cdn_edgeone.csv"
    
    if not os.path.exists(input_file):
        print(f"输入文件 {input_file} 不存在")
        return
    
    print("正在读取账单数据...")
    df = pd.read_csv(input_file)
    
    print(f"总记录数: {len(df)} 条")
    
    # 转换数值列
    numeric_columns = [
        'Total Cost', 'Total Amount After Discount (Excluding Tax)',
        'Amount Before Tax', 'TaxAmount', 'Total Cost (Including Tax)'
    ]
    
    for col in numeric_columns:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 定义分类规则
    def classify_usage(row):
        """根据规则分类使用类型"""
        
        # 客户使用的产品
        if row['ProductName'] == 'T-Sec_Anti-DDoS':
            return 'customer'
        
        # 自己使用的产品
        if row['ProductName'] in ['Elasticsearch Service', 'Tencent Container Registry', 'Message Queue CKafka']:
            return 'internal'
        
        # 根据实例ID判断客户使用的服务器
        customer_instances = [
            'ins-dyqfjlfl', 'ins-ft0g7jxj', 'ins-85iw799h', 
            'ins-1h2f3cge', 'ins-5deo4xxn', 'ins-clvpxvf0', 'ins-osc4463q'
        ]
        if pd.notna(row['InstanceID']) and row['InstanceID'] in customer_instances:
            return 'customer'
        
        # 根据实例ID判断客户使用的带宽包
        if pd.notna(row['InstanceID']) and 'bwp-0ixl0oys' in str(row['InstanceID']):
            return 'customer'
        
        # 根据实例ID判断客户使用的数据库
        if pd.notna(row['InstanceID']) and 'cdb-lh74o9ly' in str(row['InstanceID']):
            return 'customer'
        
        # 根据实例ID判断客户使用的Redis
        if pd.notna(row['InstanceID']) and 'crs-i9kk3izl' in str(row['InstanceID']):
            return 'customer'
        
        # 容器服务TKE为客户使用
        if row['ProductName'] == 'Tencent Kubernetes Engine':
            return 'customer'
        
        # 默认为内部使用
        return 'internal'
    
    # 应用分类规则
    print("正在应用分类规则...")
    df['usage_type'] = df.apply(classify_usage, axis=1)
    
    # 分离客户和内部使用的数据
    customer_df = df[df['usage_type'] == 'customer'].copy()
    internal_df = df[df['usage_type'] == 'internal'].copy()
    
    # 移除分类列
    customer_df = customer_df.drop('usage_type', axis=1)
    internal_df = internal_df.drop('usage_type', axis=1)
    
    print(f"客户使用记录数: {len(customer_df)} 条")
    print(f"内部使用记录数: {len(internal_df)} 条")
    
    # 保存分类后的原始数据
    customer_file = 'customer_usage_data.csv'
    internal_file = 'internal_usage_data.csv'
    
    customer_df.to_csv(customer_file, index=False, encoding='utf-8-sig')
    internal_df.to_csv(internal_file, index=False, encoding='utf-8-sig')
    
    print(f"\n✓ 客户使用数据已保存到: {customer_file}")
    print(f"✓ 内部使用数据已保存到: {internal_file}")
    
    # 统计客户使用情况
    print("\n" + "="*60)
    print("客户使用统计:")
    print("="*60)
    
    customer_total_before_tax = customer_df['Amount Before Tax'].sum()
    customer_total_tax = customer_df['TaxAmount'].sum()
    customer_total_with_tax = customer_df['Total Cost (Including Tax)'].sum()
    
    print(f"记录数: {len(customer_df):,}")
    print(f"税前金额: ${customer_total_before_tax:,.2f}")
    print(f"税费: ${customer_total_tax:,.2f}")
    print(f"含税总金额: ${customer_total_with_tax:,.2f}")
    
    # 客户使用按产品统计
    customer_product_summary = customer_df.groupby('ProductName').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    customer_product_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    customer_product_summary = customer_product_summary.sort_values('含税总金额', ascending=False)
    
    print(f"\n客户使用按产品分类:")
    print("-" * 40)
    for product, row in customer_product_summary.iterrows():
        print(f"  {product}: ${row['含税总金额']:,.2f}")
    
    # 保存客户使用产品统计
    customer_product_file = 'customer_product_summary.csv'
    customer_product_summary.to_csv(customer_product_file, encoding='utf-8-sig')
    print(f"\n✓ 客户产品统计已保存到: {customer_product_file}")
    
    # 统计内部使用情况
    print("\n" + "="*60)
    print("内部使用统计:")
    print("="*60)
    
    internal_total_before_tax = internal_df['Amount Before Tax'].sum()
    internal_total_tax = internal_df['TaxAmount'].sum()
    internal_total_with_tax = internal_df['Total Cost (Including Tax)'].sum()
    
    print(f"记录数: {len(internal_df):,}")
    print(f"税前金额: ${internal_total_before_tax:,.2f}")
    print(f"税费: ${internal_total_tax:,.2f}")
    print(f"含税总金额: ${internal_total_with_tax:,.2f}")
    
    # 内部使用按产品统计
    internal_product_summary = internal_df.groupby('ProductName').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    internal_product_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    internal_product_summary = internal_product_summary.sort_values('含税总金额', ascending=False)
    
    print(f"\n内部使用按产品分类 (前10名):")
    print("-" * 40)
    for product, row in internal_product_summary.head(10).iterrows():
        print(f"  {product}: ${row['含税总金额']:,.2f}")
    
    # 保存内部使用产品统计
    internal_product_file = 'internal_product_summary.csv'
    internal_product_summary.to_csv(internal_product_file, encoding='utf-8-sig')
    print(f"\n✓ 内部产品统计已保存到: {internal_product_file}")
    
    # 总体对比
    print("\n" + "="*60)
    print("总体对比:")
    print("="*60)
    
    total_before_tax = customer_total_before_tax + internal_total_before_tax
    total_tax = customer_total_tax + internal_total_tax
    total_with_tax = customer_total_with_tax + internal_total_with_tax
    
    customer_percentage = (customer_total_with_tax / total_with_tax) * 100
    internal_percentage = (internal_total_with_tax / total_with_tax) * 100
    
    print(f"客户使用占比: {customer_percentage:.1f}% (${customer_total_with_tax:,.2f})")
    print(f"内部使用占比: {internal_percentage:.1f}% (${internal_total_with_tax:,.2f})")
    print(f"总计: ${total_with_tax:,.2f}")
    
    print(f"\n所有分类文件已生成完成！")

if __name__ == "__main__":
    classify_customer_vs_internal()
