#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析COS桶的消费情况
"""

import pandas as pd
import os
import re

def analyze_cos_buckets():
    """分析COS桶的详细消费情况"""
    
    # 读取原始数据（剔除CDN和EdgeOne后的数据）
    input_file = "bill_data_excluded_cdn_edgeone.csv"
    
    if not os.path.exists(input_file):
        print(f"输入文件 {input_file} 不存在")
        return
    
    print("正在读取账单数据...")
    df = pd.read_csv(input_file)
    
    # 筛选COS相关的记录
    cos_df = df[df['ProductName'] == 'Cloud Object Storage'].copy()
    
    print(f"COS总记录数: {len(cos_df)} 条")
    
    if len(cos_df) == 0:
        print("没有找到COS相关的记录")
        return
    
    # 转换数值列
    numeric_columns = [
        'Total Cost', 'Total Amount After Discount (Excluding Tax)',
        'Amount Before Tax', 'TaxAmount', 'Total Cost (Including Tax)'
    ]
    
    for col in numeric_columns:
        if col in cos_df.columns:
            cos_df[col] = pd.to_numeric(cos_df[col], errors='coerce')
    
    def extract_bucket_info(row):
        """从配置描述中提取桶信息"""
        config_desc = str(row['Configuration Description'])
        instance_id = str(row['InstanceID'])
        
        # 尝试从InstanceID中提取桶名
        bucket_name = "未知桶"
        
        # 如果InstanceID包含桶信息
        if pd.notna(row['InstanceID']) and row['InstanceID'] != '\\N':
            bucket_name = instance_id
        
        # 尝试从配置描述中提取更详细的信息
        if 'bucket' in config_desc.lower():
            # 查找桶名模式
            bucket_match = re.search(r'bucket[:\s]*([a-zA-Z0-9\-_]+)', config_desc, re.IGNORECASE)
            if bucket_match:
                bucket_name = bucket_match.group(1)
        
        # 提取服务类型
        service_type = "标准存储"
        if 'standard' in config_desc.lower():
            service_type = "标准存储"
        elif 'infrequent' in config_desc.lower() or 'ia' in config_desc.lower():
            service_type = "低频存储"
        elif 'archive' in config_desc.lower():
            service_type = "归档存储"
        elif 'deep' in config_desc.lower():
            service_type = "深度归档"
        elif 'traffic' in config_desc.lower() or 'bandwidth' in config_desc.lower():
            service_type = "流量费用"
        elif 'request' in config_desc.lower():
            service_type = "请求费用"
        
        return bucket_name, service_type
    
    # 提取桶信息
    print("正在分析桶信息...")
    cos_df[['bucket_name', 'service_type']] = cos_df.apply(
        lambda row: pd.Series(extract_bucket_info(row)), axis=1
    )
    
    # 保存COS详细数据
    cos_detail_file = 'cos_detailed_data.csv'
    cos_df.to_csv(cos_detail_file, index=False, encoding='utf-8-sig')
    print(f"✓ COS详细数据已保存到: {cos_detail_file}")
    
    # 统计总体COS费用
    print("\n" + "="*60)
    print("COS总体统计:")
    print("="*60)
    
    total_before_tax = cos_df['Amount Before Tax'].sum()
    total_tax = cos_df['TaxAmount'].sum()
    total_with_tax = cos_df['Total Cost (Including Tax)'].sum()
    
    print(f"记录数: {len(cos_df):,}")
    print(f"税前金额: ${total_before_tax:,.2f}")
    print(f"税费: ${total_tax:,.2f}")
    print(f"含税总金额: ${total_with_tax:,.2f}")
    
    # 按桶名统计
    print(f"\n按桶名统计:")
    print("-" * 50)
    
    bucket_summary = cos_df.groupby('bucket_name').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    bucket_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    bucket_summary = bucket_summary.sort_values('含税总金额', ascending=False)
    
    for bucket, row in bucket_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {bucket}: ${row['含税总金额']:,.2f} ({percentage:.1f}%) - {row['记录数']}条记录")
    
    # 保存桶统计
    bucket_summary_file = 'cos_bucket_summary.csv'
    bucket_summary.to_csv(bucket_summary_file, encoding='utf-8-sig')
    print(f"\n✓ 桶统计已保存到: {bucket_summary_file}")
    
    # 按服务类型统计
    print(f"\n按服务类型统计:")
    print("-" * 50)
    
    service_summary = cos_df.groupby('service_type').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    service_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    service_summary = service_summary.sort_values('含税总金额', ascending=False)
    
    for service, row in service_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {service}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 保存服务类型统计
    service_summary_file = 'cos_service_summary.csv'
    service_summary.to_csv(service_summary_file, encoding='utf-8-sig')
    print(f"\n✓ 服务类型统计已保存到: {service_summary_file}")
    
    # 按桶名和服务类型双维度统计
    print(f"\n按桶名和服务类型双维度统计:")
    print("-" * 50)
    
    bucket_service_summary = cos_df.groupby(['bucket_name', 'service_type']).agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    bucket_service_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    bucket_service_summary = bucket_service_summary.sort_values('含税总金额', ascending=False)
    
    print("费用最高的前15个桶-服务组合:")
    for (bucket, service), row in bucket_service_summary.head(15).iterrows():
        print(f"  {bucket} - {service}: ${row['含税总金额']:,.2f}")
    
    # 保存桶-服务类型统计
    bucket_service_file = 'cos_bucket_service_summary.csv'
    bucket_service_summary.to_csv(bucket_service_file, encoding='utf-8-sig')
    print(f"\n✓ 桶-服务类型统计已保存到: {bucket_service_file}")
    
    # 按账户统计COS使用
    print(f"\n按账户统计COS使用:")
    print("-" * 50)
    
    account_summary = cos_df.groupby('Owner Account ID').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    account_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    account_summary = account_summary.sort_values('含税总金额', ascending=False)
    
    for account, row in account_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  账户 {account}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 保存账户统计
    account_summary_file = 'cos_account_summary.csv'
    account_summary.to_csv(account_summary_file, encoding='utf-8-sig')
    print(f"\n✓ 账户统计已保存到: {account_summary_file}")
    
    # 按项目统计COS使用
    print(f"\n按项目统计COS使用:")
    print("-" * 50)
    
    project_summary = cos_df.groupby('ProjectName').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    project_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    project_summary = project_summary.sort_values('含税总金额', ascending=False)
    
    for project, row in project_summary.iterrows():
        percentage = (row['含税总金额'] / total_with_tax) * 100
        print(f"  {project}: ${row['含税总金额']:,.2f} ({percentage:.1f}%)")
    
    # 保存项目统计
    project_summary_file = 'cos_project_summary.csv'
    project_summary.to_csv(project_summary_file, encoding='utf-8-sig')
    print(f"\n✓ 项目统计已保存到: {project_summary_file}")
    
    print(f"\n" + "="*60)
    print("COS分析建议:")
    print("="*60)
    print("1. 重点关注费用最高的桶，优化存储策略")
    print("2. 检查是否有不必要的高频访问存储，可转为低频存储")
    print("3. 分析流量费用，优化数据传输")
    print("4. 定期清理过期数据，使用生命周期管理")
    print("5. 考虑使用归档存储降低长期存储成本")
    
    print(f"\n生成的COS分析文件:")
    print("-" * 40)
    files = [
        'cos_detailed_data.csv - COS详细数据',
        'cos_bucket_summary.csv - 按桶统计',
        'cos_service_summary.csv - 按服务类型统计',
        'cos_bucket_service_summary.csv - 桶-服务类型统计',
        'cos_account_summary.csv - 按账户统计',
        'cos_project_summary.csv - 按项目统计'
    ]
    
    for file_desc in files:
        print(f"  ✓ {file_desc}")

if __name__ == "__main__":
    analyze_cos_buckets()
