#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剔除指定产品并重新生成账单拆分
"""

import pandas as pd
import os

def filter_and_regenerate():
    """剔除CDN和EdgeOne产品，重新生成拆分结果"""
    
    # 读取原始数据
    original_file = "L1-consumption_bill_by_instance-200033549284-202505-part.0.csv"
    
    if not os.path.exists(original_file):
        print(f"原始文件 {original_file} 不存在")
        return
    
    print("正在读取原始账单数据...")
    df = pd.read_csv(original_file)
    
    print(f"原始数据: {len(df)} 条记录")
    
    # 剔除指定产品
    excluded_products = ['Content Delivery Network', 'EdgeOne']
    
    print(f"剔除产品: {excluded_products}")
    
    # 过滤数据
    filtered_df = df[~df['ProductName'].isin(excluded_products)]
    
    print(f"过滤后数据: {len(filtered_df)} 条记录")
    print(f"剔除了 {len(df) - len(filtered_df)} 条记录")
    
    # 转换数值列
    numeric_columns = [
        'Total Cost', 'Total Amount After Discount (Excluding Tax)',
        'Amount Before Tax', 'TaxAmount', 'Total Cost (Including Tax)'
    ]
    
    for col in numeric_columns:
        if col in filtered_df.columns:
            filtered_df[col] = pd.to_numeric(filtered_df[col], errors='coerce')
    
    # 重新计算各种拆分
    print("\n正在重新生成拆分结果...")
    
    # 1. 按产品拆分（剔除后）
    product_summary = filtered_df.groupby('ProductName').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    product_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    product_summary = product_summary.sort_values('含税总金额', ascending=False)
    
    # 保存剔除后的产品拆分
    output_file = 'product_split_filtered.csv'
    product_summary.to_csv(output_file, encoding='utf-8-sig')
    print(f"✓ 剔除后产品拆分结果已保存到: {output_file}")
    
    # 2. 按账户拆分（剔除后）
    account_summary = filtered_df.groupby('Owner Account ID').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    account_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    account_summary = account_summary.sort_values('含税总金额', ascending=False)
    
    output_file = 'account_split_filtered.csv'
    account_summary.to_csv(output_file, encoding='utf-8-sig')
    print(f"✓ 剔除后账户拆分结果已保存到: {output_file}")
    
    # 3. 按项目拆分（剔除后）
    project_summary = filtered_df.groupby('ProjectName').agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    project_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    project_summary = project_summary.sort_values('含税总金额', ascending=False)
    
    output_file = 'project_split_filtered.csv'
    project_summary.to_csv(output_file, encoding='utf-8-sig')
    print(f"✓ 剔除后项目拆分结果已保存到: {output_file}")
    
    # 4. 按账户和项目双维度拆分（剔除后）
    account_project_summary = filtered_df.groupby(['Owner Account ID', 'ProjectName']).agg({
        'Total Cost (Including Tax)': 'sum',
        'Amount Before Tax': 'sum',
        'TaxAmount': 'sum',
        'InstanceID': 'count'
    }).round(2)
    
    account_project_summary.columns = ['含税总金额', '税前金额', '税费', '记录数']
    account_project_summary = account_project_summary.sort_values('含税总金额', ascending=False)
    
    output_file = 'account_project_split_filtered.csv'
    account_project_summary.to_csv(output_file, encoding='utf-8-sig')
    print(f"✓ 剔除后账户-项目拆分结果已保存到: {output_file}")
    
    # 5. 保存过滤后的原始数据
    filtered_data_file = 'bill_data_filtered.csv'
    filtered_df.to_csv(filtered_data_file, index=False, encoding='utf-8-sig')
    print(f"✓ 剔除后的原始数据已保存到: {filtered_data_file}")
    
    # 显示统计对比
    print("\n" + "="*60)
    print("剔除前后对比:")
    print("="*60)
    
    # 原始统计
    original_total = df['Total Cost (Including Tax)'].sum()
    original_records = len(df)
    
    # 过滤后统计
    filtered_total = filtered_df['Total Cost (Including Tax)'].sum()
    filtered_records = len(filtered_df)
    
    # 被剔除的统计
    excluded_total = original_total - filtered_total
    excluded_records = original_records - filtered_records
    
    print(f"原始数据:")
    print(f"  记录数: {original_records:,}")
    print(f"  含税总金额: ${original_total:,.2f}")
    
    print(f"\n剔除后数据:")
    print(f"  记录数: {filtered_records:,}")
    print(f"  含税总金额: ${filtered_total:,.2f}")
    
    print(f"\n被剔除数据:")
    print(f"  记录数: {excluded_records:,}")
    print(f"  含税总金额: ${excluded_total:,.2f}")
    print(f"  占原始总金额比例: {(excluded_total/original_total)*100:.1f}%")
    
    # 显示剔除后的前10个产品
    print(f"\n剔除后费用最高的前10个产品:")
    print("-" * 40)
    for i, (product, row) in enumerate(product_summary.head(10).iterrows(), 1):
        print(f"{i:2d}. {product}: ${row['含税总金额']:,.2f}")
    
    print(f"\n所有剔除后的拆分文件已生成完成！")

if __name__ == "__main__":
    filter_and_regenerate()
