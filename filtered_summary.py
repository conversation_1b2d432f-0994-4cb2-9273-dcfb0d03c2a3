#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
剔除CDN和EdgeOne后的账单拆分结果汇总
"""

import pandas as pd
import os

def display_filtered_summary():
    """显示剔除后的账单拆分汇总"""
    
    print("=" * 70)
    print("剔除CDN和EdgeOne后的腾讯云账单拆分结果汇总")
    print("=" * 70)
    
    # 读取剔除后的各个拆分结果文件
    files = {
        'account_split_filtered.csv': '按账户拆分（剔除后）',
        'project_split_filtered.csv': '按项目拆分（剔除后）', 
        'product_split_filtered.csv': '按产品拆分（剔除后）',
        'account_project_split_filtered.csv': '按账户-项目拆分（剔除后）'
    }
    
    for filename, title in files.items():
        if os.path.exists(filename):
            print(f"\n{title}:")
            print("-" * 50)
            
            df = pd.read_csv(filename)
            
            if filename == 'account_split_filtered.csv':
                print("所有账户费用明细:")
                for _, row in df.iterrows():
                    if pd.notna(row.iloc[0]):  # 检查账户ID不为空
                        print(f"  账户 {row.iloc[0]}: ${row['含税总金额']:,.2f} (记录数: {row['记录数']})")
                
                total_cost = df['含税总金额'].sum()
                print(f"\n  总计: ${total_cost:,.2f}")
                
            elif filename == 'project_split_filtered.csv':
                print("所有项目费用明细:")
                for _, row in df.iterrows():
                    print(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f} (记录数: {row['记录数']})")
                
            elif filename == 'product_split_filtered.csv':
                print("所有产品费用明细:")
                for _, row in df.iterrows():
                    print(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f} (记录数: {row['记录数']})")
                    
            elif filename == 'account_project_split_filtered.csv':
                print("费用最高的前15个账户-项目组合:")
                top_combinations = df.head(15)
                for _, row in top_combinations.iterrows():
                    print(f"  账户 {row.iloc[0]} - {row.iloc[1]}: ${row['含税总金额']:,.2f}")
    
    print("\n" + "=" * 70)
    print("剔除后关键发现:")
    print("=" * 70)
    
    # 读取剔除后的账户拆分数据进行分析
    if os.path.exists('account_split_filtered.csv'):
        account_df = pd.read_csv('account_split_filtered.csv')
        account_df = account_df.dropna()  # 移除空行
        
        if len(account_df) > 0:
            # 找出费用最高的账户
            top_account = account_df.iloc[0]
            total_cost = account_df['含税总金额'].sum()
            top_account_percentage = (top_account['含税总金额'] / total_cost) * 100
            
            print(f"1. 费用最高的账户: {top_account.iloc[0]}")
            print(f"   - 费用: ${top_account['含税总金额']:,.2f}")
            print(f"   - 占剔除后总费用比例: {top_account_percentage:.1f}%")
            print(f"   - 记录数: {top_account['记录数']}")
            
            # 找出记录数最多的账户
            max_records_account = account_df.loc[account_df['记录数'].idxmax()]
            print(f"\n2. 记录数最多的账户: {max_records_account.iloc[0]}")
            print(f"   - 记录数: {max_records_account['记录数']}")
            print(f"   - 费用: ${max_records_account['含税总金额']:,.2f}")
    
    # 读取剔除后的项目拆分数据进行分析
    if os.path.exists('project_split_filtered.csv'):
        project_df = pd.read_csv('project_split_filtered.csv')
        
        if len(project_df) > 0:
            # 找出费用最高的项目
            top_project = project_df.iloc[0]
            total_project_cost = project_df['含税总金额'].sum()
            top_project_percentage = (top_project['含税总金额'] / total_project_cost) * 100
            
            print(f"\n3. 费用最高的项目: {top_project.iloc[0]}")
            print(f"   - 费用: ${top_project['含税总金额']:,.2f}")
            print(f"   - 占剔除后总费用比例: {top_project_percentage:.1f}%")
            print(f"   - 记录数: {top_project['记录数']}")
    
    # 读取剔除后的产品拆分数据进行分析
    if os.path.exists('product_split_filtered.csv'):
        product_df = pd.read_csv('product_split_filtered.csv')
        
        if len(product_df) > 0:
            # 找出费用最高的产品
            top_product = product_df.iloc[0]
            total_product_cost = product_df['含税总金额'].sum()
            top_product_percentage = (top_product['含税总金额'] / total_product_cost) * 100
            
            print(f"\n4. 费用最高的产品: {top_product.iloc[0]}")
            print(f"   - 费用: ${top_product['含税总金额']:,.2f}")
            print(f"   - 占剔除后总费用比例: {top_product_percentage:.1f}%")
            print(f"   - 记录数: {top_product['记录数']}")
    
    # 对比原始数据
    print("\n" + "=" * 70)
    print("剔除前后对比:")
    print("=" * 70)
    
    # 读取原始账单进行对比
    original_file = "L1-consumption_bill_by_instance-************-202505-part.0.csv"
    if os.path.exists(original_file):
        original_df = pd.read_csv(original_file)
        original_total = pd.to_numeric(original_df['Total Cost (Including Tax)'], errors='coerce').sum()
        
        # 读取剔除后数据
        if os.path.exists('account_split_filtered.csv'):
            filtered_account_df = pd.read_csv('account_split_filtered.csv')
            filtered_total = filtered_account_df['含税总金额'].sum()
            
            excluded_total = original_total - filtered_total
            
            print(f"原始总费用: ${original_total:,.2f}")
            print(f"剔除后总费用: ${filtered_total:,.2f}")
            print(f"被剔除费用: ${excluded_total:,.2f}")
            print(f"剔除比例: {(excluded_total/original_total)*100:.1f}%")
    
    print("\n" + "=" * 70)
    print("剔除后优化建议:")
    print("=" * 70)
    print("1. 重点关注Cloud Object Storage和CVM的使用优化")
    print("2. 评估Technical Support Service是否必要")
    print("3. 优化数据库使用，考虑合并或降配")
    print("4. 监控负载均衡器的使用效率")
    print("5. 定期清理不必要的公网IP资源")
    print("6. 考虑使用预付费模式降低主要产品成本")

if __name__ == "__main__":
    display_filtered_summary()
