#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合汇总报告：客户使用 vs 内部使用 + CDN/EdgeOne
"""

import pandas as pd
import os
from datetime import datetime

def generate_comprehensive_summary():
    """生成综合汇总报告"""
    
    print("=" * 80)
    print("腾讯云账单综合分析报告")
    print("=" * 80)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 读取各类数据进行汇总
    files_info = {
        'customer_usage_data.csv': '客户使用',
        'internal_usage_data.csv': '内部使用',
        'cdn_edgeone_data.csv': 'CDN和EdgeOne'
    }
    
    total_summary = {}
    
    for filename, category in files_info.items():
        if os.path.exists(filename):
            df = pd.read_csv(filename)
            
            # 转换数值列
            numeric_columns = ['Amount Before Tax', 'TaxAmount', 'Total Cost (Including Tax)']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            total_summary[category] = {
                'records': len(df),
                'before_tax': df['Amount Before Tax'].sum(),
                'tax': df['TaxAmount'].sum(),
                'with_tax': df['Total Cost (Including Tax)'].sum()
            }
    
    # 计算总计
    total_records = sum([info['records'] for info in total_summary.values()])
    total_before_tax = sum([info['before_tax'] for info in total_summary.values()])
    total_tax = sum([info['tax'] for info in total_summary.values()])
    total_with_tax = sum([info['with_tax'] for info in total_summary.values()])
    
    print(f"\n总体概览:")
    print("-" * 50)
    print(f"总记录数: {total_records:,}")
    print(f"税前总金额: ${total_before_tax:,.2f}")
    print(f"税费总额: ${total_tax:,.2f}")
    print(f"含税总金额: ${total_with_tax:,.2f}")
    
    print(f"\n分类明细:")
    print("-" * 50)
    
    for category, info in total_summary.items():
        percentage = (info['with_tax'] / total_with_tax) * 100 if total_with_tax > 0 else 0
        print(f"{category}:")
        print(f"  记录数: {info['records']:,}")
        print(f"  税前金额: ${info['before_tax']:,.2f}")
        print(f"  税费: ${info['tax']:,.2f}")
        print(f"  含税金额: ${info['with_tax']:,.2f} ({percentage:.1f}%)")
        print()
    
    # 客户使用详细分析
    if os.path.exists('customer_product_summary.csv'):
        print("客户使用产品明细:")
        print("-" * 50)
        customer_products = pd.read_csv('customer_product_summary.csv')
        for _, row in customer_products.iterrows():
            print(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f}")
    
    # CDN和EdgeOne详细分析
    if os.path.exists('cdn_edgeone_summary.csv'):
        print(f"\nCDN和EdgeOne产品明细:")
        print("-" * 50)
        cdn_products = pd.read_csv('cdn_edgeone_summary.csv')
        for _, row in cdn_products.iterrows():
            print(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f}")
    
    # 内部使用主要产品
    if os.path.exists('internal_product_summary.csv'):
        print(f"\n内部使用主要产品 (前5名):")
        print("-" * 50)
        internal_products = pd.read_csv('internal_product_summary.csv')
        for _, row in internal_products.head(5).iterrows():
            print(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f}")
    
    print(f"\n" + "=" * 80)
    print("关键发现和建议:")
    print("=" * 80)
    
    # 分析各类占比
    if 'CDN和EdgeOne' in total_summary:
        cdn_percentage = (total_summary['CDN和EdgeOne']['with_tax'] / total_with_tax) * 100
        print(f"1. CDN和EdgeOne占总费用的 {cdn_percentage:.1f}%，是最大的费用项")
    
    if '客户使用' in total_summary and '内部使用' in total_summary:
        customer_percentage = (total_summary['客户使用']['with_tax'] / total_with_tax) * 100
        internal_percentage = (total_summary['内部使用']['with_tax'] / total_with_tax) * 100
        print(f"2. 在非CDN/EdgeOne费用中，客户使用占 {customer_percentage:.1f}%，内部使用占 {internal_percentage:.1f}%")
    
    print(f"3. 建议重点优化CDN和EdgeOne的使用策略")
    print(f"4. 客户使用的主要产品是防DDoS、数据库和容器服务")
    print(f"5. 内部使用的主要产品是对象存储、CVM和技术支持服务")
    
    # 生成详细报告文件
    report_filename = f"comprehensive_bill_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_filename, 'w', encoding='utf-8') as f:
        f.write("腾讯云账单综合分析报告\n")
        f.write("=" * 80 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("总体概览:\n")
        f.write("-" * 50 + "\n")
        f.write(f"总记录数: {total_records:,}\n")
        f.write(f"税前总金额: ${total_before_tax:,.2f}\n")
        f.write(f"税费总额: ${total_tax:,.2f}\n")
        f.write(f"含税总金额: ${total_with_tax:,.2f}\n\n")
        
        f.write("分类明细:\n")
        f.write("-" * 50 + "\n")
        for category, info in total_summary.items():
            percentage = (info['with_tax'] / total_with_tax) * 100 if total_with_tax > 0 else 0
            f.write(f"{category}:\n")
            f.write(f"  记录数: {info['records']:,}\n")
            f.write(f"  税前金额: ${info['before_tax']:,.2f}\n")
            f.write(f"  税费: ${info['tax']:,.2f}\n")
            f.write(f"  含税金额: ${info['with_tax']:,.2f} ({percentage:.1f}%)\n\n")
        
        # 添加详细产品信息
        if os.path.exists('customer_product_summary.csv'):
            f.write("客户使用产品明细:\n")
            f.write("-" * 50 + "\n")
            customer_products = pd.read_csv('customer_product_summary.csv')
            for _, row in customer_products.iterrows():
                f.write(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f}\n")
            f.write("\n")
        
        if os.path.exists('cdn_edgeone_summary.csv'):
            f.write("CDN和EdgeOne产品明细:\n")
            f.write("-" * 50 + "\n")
            cdn_products = pd.read_csv('cdn_edgeone_summary.csv')
            for _, row in cdn_products.iterrows():
                f.write(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f}\n")
            f.write("\n")
        
        if os.path.exists('internal_product_summary.csv'):
            f.write("内部使用主要产品:\n")
            f.write("-" * 50 + "\n")
            internal_products = pd.read_csv('internal_product_summary.csv')
            for _, row in internal_products.iterrows():
                f.write(f"  {row.iloc[0]}: ${row['含税总金额']:,.2f}\n")
    
    print(f"\n✓ 详细报告已保存到: {report_filename}")
    
    print(f"\n生成的文件列表:")
    print("-" * 50)
    generated_files = [
        'customer_usage_data.csv - 客户使用原始数据',
        'internal_usage_data.csv - 内部使用原始数据', 
        'cdn_edgeone_data.csv - CDN和EdgeOne原始数据',
        'customer_product_summary.csv - 客户产品统计',
        'internal_product_summary.csv - 内部产品统计',
        'cdn_edgeone_summary.csv - CDN和EdgeOne产品统计',
        'cdn_edgeone_account_summary.csv - CDN和EdgeOne账户统计',
        'cdn_edgeone_project_summary.csv - CDN和EdgeOne项目统计',
        f'{report_filename} - 综合分析报告'
    ]
    
    for file_desc in generated_files:
        print(f"  ✓ {file_desc}")

if __name__ == "__main__":
    generate_comprehensive_summary()
