#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
只剔除指定产品，生成新的CSV文件，不进行统计
"""

import pandas as pd
import os

def exclude_products_only():
    """只剔除CDN和EdgeOne产品，生成新的CSV文件"""
    
    # 读取原始数据
    original_file = "L1-consumption_bill_by_instance-200033549284-202505-part.0.csv"
    
    if not os.path.exists(original_file):
        print(f"原始文件 {original_file} 不存在")
        return
    
    print("正在读取原始账单数据...")
    df = pd.read_csv(original_file)
    
    print(f"原始数据: {len(df)} 条记录")
    
    # 剔除指定产品
    excluded_products = ['Content Delivery Network', 'EdgeOne']
    
    print(f"剔除产品: {excluded_products}")
    
    # 过滤数据
    filtered_df = df[~df['ProductName'].isin(excluded_products)]
    
    print(f"过滤后数据: {len(filtered_df)} 条记录")
    print(f"剔除了 {len(df) - len(filtered_df)} 条记录")
    
    # 保存过滤后的数据
    output_file = 'bill_data_excluded_cdn_edgeone.csv'
    filtered_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    print(f"\n✓ 剔除CDN和EdgeOne后的数据已保存到: {output_file}")
    
    # 显示简单统计
    print(f"\n文件信息:")
    print(f"  原始文件: {original_file}")
    print(f"  输出文件: {output_file}")
    print(f"  原始记录数: {len(df):,}")
    print(f"  输出记录数: {len(filtered_df):,}")
    print(f"  剔除记录数: {len(df) - len(filtered_df):,}")

if __name__ == "__main__":
    exclude_products_only()
